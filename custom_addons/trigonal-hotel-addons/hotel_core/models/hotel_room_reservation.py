from datetime import datetime
from odoo import fields, models, api, _
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class HotelRoomReservation(models.Model):
    _name = "hotel.room.reservation"
    _description = "Hotel Room Reservation"
    _check_company_auto = True

    @api.model
    def _valid_field_parameter(self, field, name):
        return name == 'ondelete' or super()._valid_field_parameter(field, name)

    reservation_id = fields.Many2one(
        "hotel.reservation",
        string="Reservation reference",
        index=True,
        required=True,
        ondelete="cascade",
        check_company=True,
    )
    company_id = fields.Many2one(
        "res.company", required=True, default=lambda self: self.env.company
    )

    @api.depends("company_id")
    def _default_hotel(self):
        return self.env["hotel.hotel"].search([("is_default", "=", True)], limit=1)

    hotel_id = fields.Many2one(
        "hotel.hotel",
        string="Hotel",
        default=_default_hotel,
        related='reservation_id.hotel_id',
        store=True,
        check_company=True,
        required=True,
    )

    room_type_id = fields.Many2one(
        "hotel.room.type",
        string="Room Type",
        check_company=True,
        domain="[ ('active', '=', 'True')]",
    )
    room_id = fields.Many2one(
        "hotel.room",
        string="Room",
        check_company=True,
        domain="[ ('active', '=', 'True'), ('hotel_id', '=', hotel_id), ('room_categ_id', '=', room_type_id)]",
    )
    before_transfered_room_reservation = fields.Many2one(
        "hotel.room.reservation",
        string="Room Before Transferred",
    )
    product_id = fields.Many2one(
        "product.product", related="room_id.product_id", store=True, check_company=True
    )
    expected_duration = fields.Integer(
        string="Expected stay duration (Days)",
        related="reservation_id.expected_duration",
        store=True,
        copy=True,
    )
    partner_id = fields.Many2one(
        "res.partner",
        related="reservation_id.partner_id",
        string="Partner",
        readonly=True,
        store=True,
    )
    price_unit = fields.Float(string="Unit Price", store=True)
    discount_amount = fields.Float(string="Discount Amount", default=0)
    discount_percentage = fields.Float(string="Discount Percentage(%)", default=0)
    net_price_unit = fields.Float(string="Unit Price", store=True)
    discount_granter = fields.Many2one("res.users", string="Granted By", store=True)
    checkin_date = fields.Datetime("Checkin Date", store=True)
    checkout_date = fields.Datetime("Checkout Date", store=True)
    # possible_checkout_date = fields.Datetime("Possible Checkout Date", store=True)
    actual_duration = fields.Float(
        string="Stay Duration(Days)", compute="_compute_stay_days", store=True
    )
    active = fields.Boolean(string="Active", default=True, copy=False)
    removed_reason = fields.Text(string="Removed reason", copy=False)
    order_line_id = fields.One2many(
        "sale.order.line", "room_line_id", ondelete="cascade", string="Room Line"
    )
    state = fields.Selection(string="State", related="reservation_id.state", store=True)
    status = fields.Selection(
        [("assigned", "Assigned"), ("unassigned", "Unassigned")], "Room Status"
    )
    isCheckedOut = fields.Boolean(string="Is Checked Out", default=False)

    def transfer_room(self):
        _logger.info(self.hotel_id)
        _logger.info(self.reservation_id)
        _logger.info(datetime.now())
        action = {
            "type": "ir.actions.act_window",
            "name": "Transfer Room",
            "view_mode": "form",
            "res_model": "hotel.room.reservation",
            "view_id": self.env.ref("hotel_core.action_transfer_room").id,
            "views": [
                (
                    self.env.ref("hotel_core.view_transfer_room").id,
                    "form",
                )
            ],
            "target": "new",
            "context": {
                "default_hotel_id": self.hotel_id.id,
                "default_reservation_id": self.reservation_id.id,
                "default_checkin_date": datetime.now(),
                "default_checkout_date": self.reservation_id.possible_checkout_date,
                "default_before_transfered_room_reservation": self.id,
            },
        }
        return action

    def transfer_confirm(self):
        _logger.info("Inside transfer confirm")
        _logger.info(self.room_id.id)
        _logger.info(self.before_transfered_room_reservation.id)
        # room = self.env["hotel.room.reservation"].browse(
        #     self.before_transfered_room_reservation.id
        # )
        for room in self.before_transfered_room_reservation:
            if room.room_id.active and room.room_id.status in ["occupy"]:
                room.room_id.status = "available"
            else:
                raise ValidationError(
                    "Sorry!! " + room.room_id.name + " is not checked in for check-out."
                )
            room.checkout_date = datetime.now()
            room._compute_stay_days()
            room.status = "unassigned"
            room.isCheckedOut = True
        _logger.info("Transfer confirmed")

    # @api.constrains('checkin_date', 'checkout_date', 'possible_checkout_date')
    @api.constrains("checkin_date", "checkout_date")
    def _check_dates(self):
        _logger.info("Inside check dates")
        for res in self:
            if not res.checkin_date or not res.checkout_date:
                return
            if res.checkin_date > res.checkout_date:
                raise ValidationError("Checkin date is after checkout date.")

            # _logger.info(res.room_id)
            # _logger.info(res.checkin_date)
            # _logger.info(res.checkout_date)

            room_reservation_id = res.id if res.id else None
            checkin_date = res.checkin_date
            checkout_date = res.checkout_date
            room_id = res.room_id.id
            company_id = res.company_id.id

            # Validate Double booking
            self.validate_double_booking(
                checkin_date=checkin_date,
                checkout_date=checkout_date,
                room_id=room_id,
                company_id=company_id,
                room_reservation_id=room_reservation_id,
            )

    def validate_double_booking(
        self, checkin_date, checkout_date, room_id, company_id, room_reservation_id=None
    ):
        """
        Check whether the room is booked by another reservation in given date
        #TODO: Bug::: Check underlapping dates
            Eg: Room id: 101
                Reservation: 1, checkin_date = 2023-02-14 checkout_date= 2023-02-15

        New reservation for the same room with date checkin_date = 2023-02-10 checkout_date= 2023-02-20 is allowed
        """
        _logger.info("Inside validate double booking")
        _logger.info(
            "room_id::%s, checkin_date::%s, checkin_date::%s, company_id::%s, room_reservation_id::%s",
            room_id,
            checkin_date,
            checkout_date,
            company_id,
            room_reservation_id,
        )
        domain = []

        if room_reservation_id:
            domain.append(("id", "!=", room_reservation_id))

        domain.extend(
            [
                ("room_id", "=", room_id),
                ("company_id", "=", company_id),
                ("state", "not in", ("cancel", "done")),
                "|",
                "|",
                "&",
                ("checkin_date", "<=", checkin_date),
                ("checkout_date", ">=", checkin_date),
                "&",
                ("checkin_date", "<=", checkout_date),
                ("checkout_date", ">=", checkout_date),
                "&",
                ("checkin_date", "<=", checkin_date),
                ("checkout_date", ">=", checkout_date),
            ]
        )

        _logger.info(domain)

        if self.search_count(domain) > 0:
            room_name = self.env["hotel.room"].browse(room_id).name
            raise ValidationError(
                _("The %s room is under booking for the given date range." % room_name)
            )

        # checks for underlapping dates
        domain = []

        if room_reservation_id:
            domain.append(("id", "!=", room_reservation_id))

        domain.extend(
            [
                ("room_id", "=", room_id),
                ("company_id", "=", company_id),
                ("state", "not in", ("cancel", "done")),
                ("checkin_date", ">=", checkin_date),
                ("checkout_date", "<=", checkout_date),
            ]
        )

        if self.search_count(domain) > 0:
            room_name = self.env["hotel.room"].browse(room_id).name
            raise ValidationError(
                _("The %s room is under booking for the given date range." % room_name)
            )

        return True

    @api.model
    def create(self, vals):
        _logger.info("Inside create:")
        # Check whether same room is duplicated in order line
        print(vals["reservation_id"])
        existing_room = self.search(
            [
                ("room_id", "=", vals["room_id"]),
                ("reservation_id", "=", vals["reservation_id"]),
                ("isCheckedOut", "=", False),
            ]
        )
        if existing_room:
            raise ValidationError(
                "%s Duplicate room. Room already added for this reservation."
                % existing_room.product_id.name
            )

        reservation = self.env["hotel.reservation"].browse(vals["reservation_id"])
        if "checkin_date" not in vals:
            vals["checkin_date"] = reservation.checkin_date

        if "checkout_date" not in vals and reservation.checkout_date:
            vals["checkout_date"] = reservation.checkout_date

        # if 'possible_checkout_date' not in vals and reservation.possible_checkout_date:
        #     vals['possible_checkout_date'] = reservation.possible_checkout_date

        # Add check to ensure the room is not double booked for the range
        checkout_date = vals["checkout_date"]
        checkin_date = vals["checkin_date"]
        room_id = vals["room_id"]
        # Ensure company_id is available, use default if not provided
        company_id = vals.get("company_id") or self.env.company.id
        if "company_id" not in vals:
            vals["company_id"] = company_id

        # Validate Double booking
        self.validate_double_booking(
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            room_id=room_id,
            company_id=company_id,
        )

        res = super(HotelRoomReservation, self).create(vals)

        if res.state in ["draft", "book", "confirm"]:
            room_state = "hold"
        elif res.state in ["book"]:
            room_state = "book"
        elif res.state in ["checkin"]:
            room_state = "occupy"
        else:
            room_state = "available"

        res.room_id.write({"status": room_state})

        return res

    @api.model_create_multi
    def create(self, vals_list):
        if not isinstance(vals_list, list):
            vals_list = [vals_list]
        
        for vals in vals_list:
            _logger.info("Inside create:")
            # Check whether same room is duplicated in order line
            existing_room = self.search(
                [
                    ("room_id", "=", vals["room_id"]),
                    ("reservation_id", "=", vals["reservation_id"]),
                    ("isCheckedOut", "=", False),
                ]
            )
            if existing_room:
                raise ValidationError(
                    "%s Duplicate room. Room already added for this reservation."
                    % existing_room.product_id.name
                )

            reservation = self.env["hotel.reservation"].browse(vals["reservation_id"])
            if "checkin_date" not in vals:
                vals["checkin_date"] = reservation.checkin_date

            if "checkout_date" not in vals and reservation.checkout_date:
                vals["checkout_date"] = reservation.checkout_date

            # Add check to ensure the room is not double booked for the range
            checkout_date = vals["checkout_date"]
            checkin_date = vals["checkin_date"]
            room_id = vals["room_id"]
            # Ensure company_id is available, use default if not provided
            company_id = vals.get("company_id") or self.env.company.id
            if "company_id" not in vals:
                vals["company_id"] = company_id

            # Validate Double booking
            self.validate_double_booking(
                checkin_date=checkin_date,
                checkout_date=checkout_date,
                room_id=room_id,
                company_id=company_id,
            )

        res = super(HotelRoomReservation, self).create(vals_list)
        
        # Update room status for all created records
        for record in res:
            if record.state in ["draft", "book", "confirm"]:
                room_state = "hold"
            elif record.state in ["book"]:
                room_state = "book"
            elif record.state in ["checkin"]:
                room_state = "occupy"
            else:
                room_state = "available"
            
            record.room_id.write({"status": room_state})

        return res

    def write(self, vals):
        _logger.info("Inside write")
        # Check whether same room is duplicated in order line
        if "room_id" in vals:
            existing_room = self.search(
                [
                    ("id", "!=", self.id),
                    ("room_id", "=", vals["room_id"]),
                    ("reservation_id", "=", self.reservation_id.id),
                ]
            )
            if existing_room:
                raise ValidationError(
                    "%s Duplicate room. Room already added for this reservation."
                    % existing_room.product_id.name
                )

        room_id = vals["room_id"] if "room_id" in vals else self.room_id.id
        checkin_date = (
            vals["checkin_date"] if "checkin_date" in vals else self.checkin_date
        )
        checkout_date = (
            vals["checkout_date"] if "checkout_date" in vals else self.checkout_date
        )
        company_id = self.company_id.id
        room_reservation_id = self.id

        self.validate_double_booking(
            room_id=room_id,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            company_id=company_id,
            room_reservation_id=room_reservation_id,
        )

        res = super(HotelRoomReservation, self).write(vals)

        if "room_id" in vals:
            if self.state in ["draft", "booked", "confirmed"]:
                room_state = "hold"
            elif self.state in ["booked"]:
                room_state = "book"
            elif self.state in ["checkin"]:
                room_state = "occupy"
            else:
                room_state = "available"
            self.room_id.write({"status": room_state})

        return res

    @api.depends("reservation_id.checkin_date", "reservation_id.checkout_date")
    # @api.onchange("checkin_date", "checkout_date", "possible_checkout_date")
    def _compute_stay_days(self):
        _logger.info("Inside _compute_stay_days")
        for book in self:
            checkin_date = book.checkin_date
            if not checkin_date:
                checkin_date = book.reservation_id.checkin_date
            if not checkin_date:
                checkin_date = book.reservation_id.possible_checkin_date

            if not checkin_date:
                raise ValidationError("Sorry!! Checkin date is not entered")

            checkout_date = book.checkout_date
            if not checkout_date:
                checkout_date = book.reservation_id.checkout_date

            if not checkout_date:
                checkout_date = book.reservation_id.possible_checkout_date

            if not checkout_date:
                raise ValidationError("Sorry!! Checkout date is not entered")

            if checkin_date > checkout_date:
                raise ValidationError(
                    "Checkin date is after checkout date. Please check the checkin and checkout dates."
                )

            book.checkin_date = checkin_date
            book.checkout_date = checkout_date

            room_id = book.room_id.id
            company_id = book.company_id.id
            room_reservation_id = book.id if book.id else None

            self.validate_double_booking(
                room_id=room_id,
                company_id=company_id,
                checkin_date=book.checkin_date,
                checkout_date=book.checkout_date,
                room_reservation_id=room_reservation_id,
            )

            # Compute estimated time
            number_of_days = 0

            if book.checkout_date and book.checkin_date:
                server_dt = DEFAULT_SERVER_DATETIME_FORMAT
                difference = book.checkout_date - book.checkin_date

                number_of_days = difference.days

                # Convert seconds to hours
                additional_hours = abs(difference.seconds / 3600)

                # Compute additional stay days
                book.actual_duration = number_of_days
                vals = self.env["res.config.settings"].sudo().get_values()
                extra_hours = int(
                    vals["additional_hours"] if vals["additional_hours"] else 12
                )

                if additional_hours > extra_hours:
                    # Add 1 day for additional hours stayed beyond allowed hours
                    number_of_days += 1

                if number_of_days == 0:
                    number_of_days = 1

            book.expected_duration = number_of_days
            book.actual_duration = number_of_days

    @api.onchange("room_id")
    def _on_change_room(self):
        _logger.info("Inside change room")

        for book in self:
            if not book.room_id:
                return

            checkin_date = book.checkin_date
            if not checkin_date:
                checkin_date = book.reservation_id.checkin_date
            if not checkin_date:
                checkin_date = book.reservation_id.possible_checkin_date

            if not checkin_date:
                raise ValidationError("Sorry!! Checkin date is not entered")

            book.checkin_date = checkin_date

            checkout_date = book.checkout_date
            if not checkout_date:
                checkout_date = book.checkout_date

            if not checkout_date:
                checkout_date = book.reservation_id.checkout_date

            if not checkout_date:
                checkout_date = book.reservation_id.possible_checkout_date

            if not checkout_date:
                raise ValidationError("Sorry!! Checkout date is not entered")

            book.checkout_date = checkout_date
            room_reservation_id = book.id if book.id else None
            room_id = book.room_id

            self.validate_double_booking(
                room_id=room_id.id,
                company_id=book.company_id.id,
                checkin_date=book.checkin_date,
                checkout_date=book.checkout_date,
                room_reservation_id=room_reservation_id,
            )

    @api.onchange("discount_percentage", "discount_amount")
    def _onchange_discount(self):
        _logger.info("Inside discount change")
        for res in self:
            res.discount_granter = self.env.user

    @api.onchange("price_unit", "discount_percentage", "discount_amount")
    def _compute_unit_price(self):
        _logger.info("Inside _compute_unit_price change")
        for res in self:
            # if res.room_id and res.room_id.active and res.room_id.status == "available":
            if res.room_id and res.room_id.active:
                res._compute_price_common()

    @api.onchange("room_id")
    def _compute_price(self):
        print("Inside _compute_price change")
        for res in self:
            # if res.room_id and res.room_id.active and res.room_id.status == "available":
            if res.room_id and res.room_id.active:
                res.product_id = res.room_id.product_id
                res.price_unit = res.product_id.list_price
                res._compute_price_common()

    def _compute_price_common(self):
        self.net_price_unit = self.price_unit
        if self.discount_amount:
            self.net_price_unit -= self.discount_amount

    @api.onchange("discount_percentage")
    def _onchange_discount_percentage(self):
        for res in self:
            if res.price_unit > 0:
                res.discount_amount = res.discount_percentage * res.price_unit / 100

    @api.onchange("discount_amount")
    def _onchange_discount_amount(self):
        for res in self:
            if res.price_unit > 0:
                res.discount_percentage = res.discount_amount * 100 / res.price_unit
