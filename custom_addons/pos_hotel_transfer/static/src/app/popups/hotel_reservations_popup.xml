<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_hotel_transfer.HotelReservationsPopup" owl="1">
        <Dialog size="'lg'" title="'Hotel Reservations'">
            <t t-set-slot="header">
                <h4 class="modal-title">
                    <i class="fa fa-hotel me-2"/>
                    Hotel Reservations
                </h4>
            </t>

            <div class="dialog-body">
                <!-- Loading State -->
                <div t-if="state.loading" class="text-center p-4">
                    <i class="fa fa-spinner fa-spin fa-2x mb-3"/>
                    <p>Loading hotel reservations...</p>
                </div>

                <!-- Error State -->
                <div t-if="state.error" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle me-2"/>
                    <t t-esc="state.error"/>
                </div>

                <!-- Reservations List -->
                <div t-if="!state.loading and !state.error" class="reservations-container">
                    <div t-if="state.reservations.length === 0" class="text-center p-4">
                        <i class="fa fa-info-circle fa-2x mb-3 text-muted"/>
                        <p class="text-muted">No active hotel reservations found</p>
                    </div>

                    <div t-if="state.reservations.length > 0" class="reservations-list">
                        <div class="reservation-header">
                            <p class="mb-3">
                                <strong>Select a room to transfer the order:</strong>
                                <small class="text-muted d-block">Each room from multi-room reservations is shown separately</small>
                            </p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Select</th>
                                        <th>Room</th>
                                        <th>Reservation</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="state.reservations" t-as="reservation" t-key="reservation.id"
                                        t-att-class="state.selectedReservation?.id === reservation.id ? 'table-primary' : ''"
                                        class="reservation-row"
                                        t-on-click="() => this.selectReservation(reservation)">
                                        <td>
                                            <input type="radio"
                                                   t-att-checked="state.selectedReservation?.id === reservation.id"
                                                   t-on-change="() => this.selectReservation(reservation)"/>
                                        </td>
                                        <td>
                                            <span class="badge bg-info me-1">
                                                <i class="fa fa-bed me-1"/>
                                                <t t-esc="reservation.room_number"/>
                                            </span>
                                        </td>
                                        <td>
                                            <strong t-esc="reservation.reservation_name"/>
                                        </td>
                                        <td>
                                            <span t-att-class="'badge ' + (reservation.state === 'checkin' ? 'bg-success' : 'bg-warning')">
                                                <t t-esc="reservation.state_label"/>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Selected Room Details -->
                        <div t-if="state.selectedReservation" class="selected-reservation mt-3 p-3 bg-light rounded">
                            <h6 class="mb-2">
                                <i class="fa fa-check-circle text-success me-1"/>
                                Selected Room Details
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>Room:</strong> <t t-esc="state.selectedReservation.room_number"/></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Reservation:</strong> <t t-esc="state.selectedReservation.reservation_name"/></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Status:</strong>
                                        <span t-att-class="'badge ' + (state.selectedReservation.state === 'checkin' ? 'bg-success' : 'bg-warning')">
                                            <t t-esc="state.selectedReservation.state_label"/>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <t t-set-slot="footer">
                <button class="btn btn-secondary" t-on-click="cancel">
                    <i class="fa fa-times me-1"/>
                    Cancel
                </button>
                <button class="btn btn-primary"
                        t-att-disabled="!state.selectedReservation"
                        t-on-click="transferToReservation">
                    <i class="fa fa-exchange me-1"/>
                    Transfer to Room
                </button>
            </t>
        </Dialog>
    </t>
</templates>
